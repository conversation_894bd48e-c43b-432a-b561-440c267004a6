import { PrintOptions, Printer, print } from "pdf-to-printer";
import { savePDF } from "./pdfHelper";
import { Content } from "pdfmake/interfaces";
import { fileToSavePath } from "../constants";
import { getPrinterNames } from "../core/printers";
import { isPkg } from "../utils/global-utils";
import { FilesEnums } from "../constants/files";

// handle specific printer to print and its options
function handlePrinterOptions(printerId: string): PrintOptions {
    return {
        printer: printerId,
        scale: "shrink",
        sumatraPdfPath: isPkg ? `./${FilesEnums.SumatraPDF}` : undefined,
    };
}

// start printing process
export async function startPrint(printerId: string, fileToPrintPath: string) {
    try {
        const options = handlePrinterOptions(printerId);
        await print(fileToPrintPath, options);
    } catch (error) {
        console.error(error);
        throw error;
    }
}

export async function saveAndPrint(
    printerId: string,
    content: Content[] | Content,
    filePath: string = fileToSavePath
) {
    try {
        await savePDF(content, filePath);
        await startPrint(printerId, filePath);
    } catch (error) {
        console.error(error);
        throw error;
    }
}

// get all printers names and ids
export const getAllPrinters = async (): Promise<Printer[]> => {
    try {
        const printers = await getPrinterNames();
        return printers.map(printer => ({
            deviceId: printer,
            name: printer,
            paperSizes: []
        }));
    } catch (error) {
        console.error(error);
        throw error;
    }
};